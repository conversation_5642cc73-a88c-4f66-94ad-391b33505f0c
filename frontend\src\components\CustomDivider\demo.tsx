import React from 'react';
import { Card, Space, Typography } from 'antd';
import CustomDivider from './index';

const { Title, Text } = Typography;

/**
 * CustomDivider 组件使用示例
 */
const CustomDividerDemo: React.FC = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>CustomDivider 组件示例</Title>
      
      <Card title="基础用法" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>默认水平分隔线</Text>
          <CustomDivider />
          <Text>自定义颜色的分隔线</Text>
          <CustomDivider color="#1890ff" />
          <Text>自定义粗细的分隔线</Text>
          <CustomDivider thickness={3} color="#52c41a" />
        </Space>
      </Card>

      <Card title="垂直分隔线" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', height: 100 }}>
          <Text>左侧内容</Text>
          <CustomDivider 
            orientation="vertical" 
            color="#ff4d4f" 
            margin="0 16px"
            style={{ height: 60 }}
          />
          <Text>右侧内容</Text>
        </div>
      </Card>

      <Card title="在卡片中使用" style={{ marginBottom: 16 }}>
        <div>
          <Text strong>标题内容</Text>
          <CustomDivider color="#d9d9d9" margin="12px 0" />
          <Text>这里是正文内容，分隔线将标题和正文分开。</Text>
          <CustomDivider color="#f0f0f0" margin="16px 0" />
          <Text type="secondary">这里是次要信息。</Text>
        </div>
      </Card>

      <Card title="透明度和样式" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>半透明分隔线</Text>
          <CustomDivider color="#1890ff" opacity={0.5} />
          <Text>带圆角的分隔线</Text>
          <CustomDivider 
            color="#722ed1" 
            thickness={4} 
            style={{ borderRadius: 2 }}
          />
          <Text>渐变效果（通过自定义样式）</Text>
          <CustomDivider 
            style={{ 
              background: 'linear-gradient(90deg, #1890ff, #722ed1)',
              height: 2,
              borderRadius: 1
            }}
          />
        </Space>
      </Card>
    </div>
  );
};

export default CustomDividerDemo;

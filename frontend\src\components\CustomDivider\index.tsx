import React from 'react';

/**
 * 可复用的分隔线组件
 * 可在整个应用程序中通用使用
 */
interface CustomDividerProps {
  /** 分隔线颜色 */
  color?: string;
  /** 透明度 */
  opacity?: number;
  /** 外边距 */
  margin?: string | number;
  /** 方向：水平或垂直 */
  orientation?: 'horizontal' | 'vertical';
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 分隔线粗细 */
  thickness?: number;
  /** 类名 */
  className?: string;
}

const CustomDivider: React.FC<CustomDividerProps> = ({
  color = '#f0f0f0',
  opacity = 1,
  margin = '16px 0',
  orientation = 'horizontal',
  thickness = 1,
  style = {},
  className,
}) => {
  const dividerStyle: React.CSSProperties = {
    backgroundColor: color,
    opacity,
    border: 'none',
    ...(orientation === 'horizontal' 
      ? { 
          height: `${thickness}px`, 
          width: '100%', 
          margin: typeof margin === 'string' ? margin : `${margin}px 0`
        }
      : { 
          width: `${thickness}px`, 
          height: '100%', 
          margin: typeof margin === 'string' ? margin : `0 ${margin}px`
        }
    ),
    ...style,
  };

  return <div className={className} style={dividerStyle} />;
};

export default CustomDivider;
export type { CustomDividerProps };

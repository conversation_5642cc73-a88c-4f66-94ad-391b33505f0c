import React from 'react';
import { render } from '@testing-library/react';
import CustomDivider from '../index';

describe('CustomDivider', () => {
  it('renders horizontal divider by default', () => {
    const { container } = render(<CustomDivider />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toBeInTheDocument();
    expect(divider).toHaveStyle({
      backgroundColor: '#f0f0f0',
      height: '1px',
      width: '100%',
    });
  });

  it('renders vertical divider when orientation is vertical', () => {
    const { container } = render(<CustomDivider orientation="vertical" />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toHaveStyle({
      width: '1px',
      height: '100%',
    });
  });

  it('applies custom color', () => {
    const customColor = 'rgba(255,0,0,0.5)';
    const { container } = render(<CustomDivider color={customColor} />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toHaveStyle({
      backgroundColor: customColor,
    });
  });

  it('applies custom thickness', () => {
    const { container } = render(<CustomDivider thickness={3} />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toHaveStyle({
      height: '3px',
    });
  });

  it('applies custom styles', () => {
    const customStyle = { borderRadius: '2px' };
    const { container } = render(<CustomDivider style={customStyle} />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toHaveStyle({
      borderRadius: '2px',
    });
  });

  it('applies custom className', () => {
    const { container } = render(<CustomDivider className="custom-divider" />);
    const divider = container.firstChild as HTMLElement;
    
    expect(divider).toHaveClass('custom-divider');
  });
});
